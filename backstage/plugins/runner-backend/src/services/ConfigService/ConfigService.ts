import { UrlReaderService } from '@backstage/backend-plugin-api';
import { Entity } from '@backstage/catalog-model';
import { RunnerConfig } from '../RunnerService/types';
import * as yaml from 'yaml';

export class ConfigService {
  constructor(private urlReader: UrlReaderService) {}

  async getRunnerConfig(entity: Entity): Promise<RunnerConfig> {
    const configPath = entity.metadata.annotations?.['runner.backstage.io/config-path'] || '.runner/config.yml';
    const sourceLocation = entity.metadata.annotations?.['backstage.io/source-location'];

    if (!sourceLocation) {
      throw new Error('Component missing source location annotation');
    }

    // Parse the source location to construct the correct config URL
    const configUrl = this.constructConfigUrl(sourceLocation, configPath);

    try {
      const response = await this.urlReader.readUrl(configUrl);
      const configContent = await response.buffer();
      const config = yaml.parse(configContent.toString());

      return this.validateConfig(config.runner);
    } catch (error) {
      throw new Error(`Failed to read runner configuration: ${error}`);
    }
  }

  private constructConfigUrl(sourceLocation: string, configPath: string): string {
    // Remove any trailing slashes
    let baseUrl = sourceLocation.replace(/\/$/, '');

    // Handle different GitHub URL formats
    if (baseUrl.includes('/tree/')) {
      // Convert tree URL to blob URL
      // e.g., https://github.com/user/repo/tree/main -> https://github.com/user/repo/blob/main
      baseUrl = baseUrl.replace('/tree/', '/blob/');
    } else if (!baseUrl.includes('/blob/')) {
      // If no blob or tree in URL, assume it's a repository root and add blob/main
      // e.g., https://github.com/user/repo -> https://github.com/user/repo/blob/main
      baseUrl = `${baseUrl}/blob/main`;
    }

    // Ensure configPath doesn't start with a slash to avoid double slashes
    const cleanConfigPath = configPath.startsWith('/') ? configPath.substring(1) : configPath;

    return `${baseUrl}/${cleanConfigPath}`;
  }

  private validateConfig(config: any): RunnerConfig {
    if (!config || config.type !== 'docker') {
      throw new Error('Invalid runner configuration: type must be "docker"');
    }

    if (!config.dockerfile) {
      throw new Error('Invalid runner configuration: dockerfile is required');
    }

    if (!config.ports || !Array.isArray(config.ports) || config.ports.length === 0) {
      throw new Error('Invalid runner configuration: ports array is required');
    }

    return {
      type: 'docker',
      dockerfile: config.dockerfile,
      ports: config.ports,
      environment: config.environment || {},
      healthCheck: config.healthCheck,
      build: config.build || { context: '.' }
    };
  }
}
